'use client';

import { useEffect } from 'react';
import { usePortfolioStore } from '@/store/usePortfolioStore';

interface ThemeProviderProps {
  children: React.ReactNode;
}

const ThemeProvider = ({ children }: ThemeProviderProps) => {
  const { theme, setTheme } = usePortfolioStore();

  useEffect(() => {
    // Initialize theme on mount
    const initializeTheme = () => {
      // Check if there's a saved theme in localStorage
      const savedTheme = localStorage.getItem('portfolio-storage');
      if (savedTheme) {
        try {
          const parsed = JSON.parse(savedTheme);
          const themeFromStorage = parsed.state?.theme;
          if (themeFromStorage && (themeFromStorage === 'light' || themeFromStorage === 'dark')) {
            setTheme(themeFromStorage);
            document.documentElement.setAttribute('data-theme', themeFromStorage);
            return;
          }
        } catch (error) {
          console.error('Error parsing saved theme:', error);
        }
      }

      // Check system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      const systemTheme = prefersDark ? 'dark' : 'light';
      setTheme(systemTheme);
      document.documentElement.setAttribute('data-theme', systemTheme);
    };

    initializeTheme();

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const handleChange = (e: MediaQueryListEvent) => {
      // Only update if no theme is saved in localStorage
      const savedTheme = localStorage.getItem('portfolio-storage');
      if (!savedTheme) {
        const newTheme = e.matches ? 'dark' : 'light';
        setTheme(newTheme);
        document.documentElement.setAttribute('data-theme', newTheme);
      }
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, [setTheme]);

  useEffect(() => {
    // Update document attribute when theme changes
    document.documentElement.setAttribute('data-theme', theme);
  }, [theme]);

  return <>{children}</>;
};

export default ThemeProvider;
