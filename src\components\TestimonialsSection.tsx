'use client';

import { useRef, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { FaQuoteLeft, FaChevronLeft, FaChevronRight } from 'react-icons/fa';
import Image from 'next/image';

interface Testimonial {
  id: string;
  name: string;
  position: string;
  company: string;
  content: string;
  avatar: string;
}

const TestimonialsSection = () => {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, margin: '-100px' });
  
  const [currentIndex, setCurrentIndex] = useState(0);

  // Sample testimonials data (matching the reference portfolio)
  const testimonials: Testimonial[] = [
    {
      id: '1',
      name: '<PERSON>',
      position: 'Founder',
      company: 'Tech Solutions Inc.',
      content: 'Since 2020 <PERSON> has been responsible for the development of our website which has been instrumental to the growth of our company. Even while working remotely he\'s been highly responsive, organized and strategic in his thinking. In addition to staying on top of day-to-day site changes and builds, he\'s provided us with great advice to stay on top of the current changes in web technologies.',
      avatar: '/profile-placeholder.jpg'
    },
    {
      id: '2',
      name: '<PERSON>',
      position: 'Product Manager',
      company: 'Digital Innovations',
      content: '<PERSON> is AMAZING! If you have any doubt about hiring him, ask me – I am really impressed by this guy! His attention to detail and ability to deliver complex projects on time is outstanding.',
      avatar: '/profile-placeholder.jpg'
    },
    {
      id: '3',
      name: 'Michael Chen',
      position: 'CEO & Founder',
      company: 'StartupFlow',
      content: 'Joseph is one of the best professionals that we have known in web development skills. Between his skills you can find good communication and accuracy with the planning in complex projects.',
      avatar: '/profile-placeholder.jpg'
    }
  ];

  const nextTestimonial = () => {
    setCurrentIndex((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section ref={sectionRef} className="py-20 relative">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="text-foreground">Client </span>
            <span className="text-primary">Testimonials</span>
          </h2>
          <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            What clients say about working with me
          </p>
        </motion.div>

        {/* Testimonials Carousel */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-4xl mx-auto"
        >
          <motion.div
            variants={itemVariants}
            className="relative"
          >
            {/* Main Testimonial Card */}
            <div className="glass rounded-2xl p-8 md:p-12 border border-border relative overflow-hidden">
              {/* Quote Icon */}
              <motion.div
                className="absolute top-6 left-6 text-primary/20"
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 0.6, delay: 0.5 }}
              >
                <FaQuoteLeft className="w-12 h-12" />
              </motion.div>

              {/* Testimonial Content */}
              <motion.div
                key={currentIndex}
                initial={{ opacity: 0, x: 50 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -50 }}
                transition={{ duration: 0.5 }}
                className="relative z-10"
              >
                <blockquote className="text-lg md:text-xl text-foreground leading-relaxed mb-8 font-light">
                  "{testimonials[currentIndex].content}"
                </blockquote>

                {/* Client Info */}
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 rounded-full overflow-hidden bg-secondary">
                    <Image
                      src={testimonials[currentIndex].avatar}
                      alt={testimonials[currentIndex].name}
                      width={64}
                      height={64}
                      className="object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.style.display = 'none';
                      }}
                    />
                  </div>
                  <div>
                    <h4 className="font-bold text-foreground">
                      {testimonials[currentIndex].name}
                    </h4>
                    <p className="text-muted-foreground text-sm">
                      {testimonials[currentIndex].position} at{' '}
                      <span className="text-primary">
                        {testimonials[currentIndex].company}
                      </span>
                    </p>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-center items-center gap-4 mt-8">
              <motion.button
                onClick={prevTestimonial}
                className="w-12 h-12 rounded-full glass border border-border hover:border-primary/50 flex items-center justify-center text-muted-foreground hover:text-primary transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <FaChevronLeft className="w-4 h-4" />
              </motion.button>

              {/* Dots Indicator */}
              <div className="flex gap-2">
                {testimonials.map((_, index) => (
                  <motion.button
                    key={index}
                    onClick={() => setCurrentIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-300 ${
                      index === currentIndex
                        ? 'bg-primary'
                        : 'bg-border hover:bg-primary/50'
                    }`}
                    whileHover={{ scale: 1.2 }}
                    whileTap={{ scale: 0.8 }}
                  />
                ))}
              </div>

              <motion.button
                onClick={nextTestimonial}
                className="w-12 h-12 rounded-full glass border border-border hover:border-primary/50 flex items-center justify-center text-muted-foreground hover:text-primary transition-all duration-300"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                <FaChevronRight className="w-4 h-4" />
              </motion.button>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
