@import "tailwindcss";

/* Light theme variables */
:root {
  --background: #ffffff;
  --foreground: #1a1a1a;
  --muted: #6b7280;
  --muted-foreground: #9ca3af;
  --card: #ffffff;
  --card-foreground: #1a1a1a;
  --border: #e5e7eb;
  --input: #f9fafb;
  --primary: #ff5722;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #374151;
  --accent: #ff8a65;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #ff5722;
  --radius: 0.5rem;
}

/* Dark theme variables */
[data-theme="dark"] {
  --background: #0a0a0a;
  --foreground: #fafafa;
  --muted: #737373;
  --muted-foreground: #a3a3a3;
  --card: #1a1a1a;
  --card-foreground: #fafafa;
  --border: #262626;
  --input: #171717;
  --primary: #ff5722;
  --primary-foreground: #ffffff;
  --secondary: #262626;
  --secondary-foreground: #fafafa;
  --accent: #ff8a65;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --ring: #ff5722;
}

/* Legacy variables for backward compatibility */
:root {
  --accent-primary: var(--primary);
  --accent-secondary: var(--accent);
  --accent-tertiary: #e64a19;
  --card-bg: var(--card);
  --card-hover: var(--secondary);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
  overflow-x: hidden;
}

/* Theme transition for all elements */
* {
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
}

/* Smooth scroll enhancement */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }
}

/* Image loading animation */
img {
  transition: opacity 0.3s ease;
}

img[data-loaded="false"] {
  opacity: 0;
}

img[data-loaded="true"] {
  opacity: 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--secondary);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, var(--primary), var(--accent));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, var(--accent-tertiary), var(--accent));
}

/* Neon glow effects */
.neon-glow {
  text-shadow: 0 0 5px rgba(255, 87, 34, 0.7), 0 0 10px rgba(255, 87, 34, 0.5), 0 0 15px rgba(255, 87, 34, 0.3);
}

.neon-box {
  box-shadow: 0 0 5px rgba(255, 87, 34, 0.7), 0 0 10px rgba(255, 87, 34, 0.5), 0 0 15px rgba(255, 87, 34, 0.3);
}

.neon-border {
  position: relative;
}

.neon-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--accent-primary), var(--accent-secondary), var(--accent-tertiary));
  z-index: -1;
  border-radius: inherit;
  animation: border-animation 3s linear infinite;
}

@keyframes border-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Glass morphism - theme aware */
.glass {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--border);
}

[data-theme="dark"] .glass {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

:root .glass {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Animated gradient background */
.gradient-bg {
  background: linear-gradient(-45deg, #ff5722, #ff8a65, #e64a19, #ff7043);
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Floating animations for cubes */
@keyframes float-slow {
  0%, 100% { transform: translateY(0) rotate(12deg); }
  50% { transform: translateY(-20px) rotate(15deg); }
}

@keyframes float-medium {
  0%, 100% { transform: translateY(0) rotate(-12deg); }
  50% { transform: translateY(-15px) rotate(-15deg); }
}

@keyframes float-fast {
  0%, 100% { transform: translateY(0) rotate(45deg); }
  50% { transform: translateY(-10px) rotate(40deg); }
}

.animate-float-slow {
  animation: float-slow 6s ease-in-out infinite;
}

.animate-float-medium {
  animation: float-medium 4s ease-in-out infinite;
}

.animate-float-fast {
  animation: float-fast 3s ease-in-out infinite;
}

/* Line clamp utility for review text */
.line-clamp-4 {
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Review card hover effects */
.review-card {
  transition: all 0.3s ease;
}

.review-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(255, 87, 34, 0.1);
}

/* Enhanced scroll animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility classes for animations */
.animate-slide-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-slide-left {
  animation: slideInLeft 0.6s ease-out;
}

.animate-slide-right {
  animation: slideInRight 0.6s ease-out;
}

.animate-fade-scale {
  animation: fadeInScale 0.6s ease-out;
}

/* Enhanced glass effect */
.glass-enhanced {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Glow on hover */
.glow-on-hover:hover {
  box-shadow: 0 0 20px rgba(255, 87, 34, 0.3);
  transition: box-shadow 0.3s ease;
}

/* Pulse animation for important elements */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(255, 87, 34, 0.4);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 87, 34, 0.8);
  }
}

.pulse-glow {
  animation: pulse-glow 2s infinite;
}
