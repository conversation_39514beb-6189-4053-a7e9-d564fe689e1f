'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FaQuoteLeft, FaUser, FaCalendarAlt } from 'react-icons/fa';
import StarRating from './StarRating';
import { reviewsApi, Review } from '@/lib/supabase';

const ReviewsDisplay = () => {
  const [reviews, setReviews] = useState<Review[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  const REVIEWS_PER_PAGE = 6;

  const fetchReviews = async (pageNum = 0, append = false) => {
    try {
      setLoading(true);
      setError(null);

      console.log('Fetching reviews, page:', pageNum);
      const { data, error, count } = await reviewsApi.getApprovedReviews(pageNum, REVIEWS_PER_PAGE);

      console.log('Reviews response:', { data, error, count });

      if (error) {
        console.error('Reviews fetch error:', error);
        setError(typeof error === 'string' ? error : 'Failed to fetch reviews');
        return;
      }

      if (data) {
        if (append) {
          setReviews(prev => [...prev, ...data]);
        } else {
          setReviews(data);
        }
        setTotalCount(count || 0);
        setHasMore(data.length === REVIEWS_PER_PAGE && (pageNum + 1) * REVIEWS_PER_PAGE < (count || 0));
      } else {
        setReviews([]);
        setTotalCount(0);
        setHasMore(false);
      }
    } catch (err) {
      console.error('Fetch reviews exception:', err);
      setError(err instanceof Error ? err.message : 'Failed to load reviews');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReviews();
  }, []);

  const loadMore = () => {
    const nextPage = page + 1;
    setPage(nextPage);
    fetchReviews(nextPage, true);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: { y: 0, opacity: 1 }
  };

  if (loading && reviews.length === 0) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--accent-primary)]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-6 max-w-md mx-auto">
          <p className="text-red-400 mb-4">{error}</p>
          <button
            onClick={() => {
              setError(null);
              setPage(0);
              fetchReviews();
            }}
            className="bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white px-4 py-2 rounded-full hover:shadow-lg transition-all"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (reviews.length === 0 && !loading) {
    return (
      <div className="text-center py-12">
        <div className="bg-gray-800 rounded-lg p-8 max-w-md mx-auto">
          <FaQuoteLeft className="text-4xl text-[var(--accent-primary)] mx-auto mb-4 opacity-50" />
          <h3 className="text-xl font-semibold mb-2 text-foreground">No Reviews Yet</h3>
          <p className="text-foreground/70">
            Be the first to share your experience working with me!
          </p>
        </div>
      </div>
    );
  }

  return (
    <div>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
      >
        {reviews.map((review) => (
          <motion.div
            key={review.id}
            variants={itemVariants}
            className="bg-gray-800 rounded-lg p-6 relative overflow-hidden group hover:bg-gray-750 transition-all duration-300 review-card"
          >
            {/* Quote Icon */}
            <FaQuoteLeft className="absolute top-4 right-4 text-2xl text-[var(--accent-primary)] opacity-20" />
            
            {/* Rating */}
            <div className="mb-4">
              <StarRating rating={review.rating} readonly size="sm" />
            </div>

            {/* Review Text */}
            <p className="text-foreground/80 mb-4 leading-relaxed line-clamp-4">
              "{review.review_text}"
            </p>

            {/* Reviewer Info */}
            <div className="flex items-center justify-between pt-4 border-t border-gray-700">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] flex items-center justify-center mr-3">
                  <FaUser className="text-white text-sm" />
                </div>
                <div>
                  <p className="text-foreground font-medium text-sm">{review.reviewer_name}</p>
                  <div className="flex items-center text-foreground/50 text-xs">
                    <FaCalendarAlt className="mr-1" />
                    {formatDate(review.created_at)}
                  </div>
                </div>
              </div>
            </div>

            {/* Hover Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-[var(--accent-primary)]/5 to-[var(--accent-secondary)]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          </motion.div>
        ))}
      </motion.div>

      {/* Load More Button */}
      {hasMore && (
        <div className="text-center mt-8">
          <button
            onClick={loadMore}
            disabled={loading}
            className="bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white px-8 py-3 rounded-full hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading...
              </>
            ) : (
              `Load More Reviews (${totalCount - reviews.length} remaining)`
            )}
          </button>
        </div>
      )}
    </div>
  );
};

export default ReviewsDisplay;
