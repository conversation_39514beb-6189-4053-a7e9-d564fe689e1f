# Portfolio Website with Customer Reviews

A modern, interactive portfolio website built with Next.js, featuring a customer review system powered by Supabase.

## Features

- **Modern Portfolio Design**: Clean, responsive design with dark theme and orange accents
- **3D Interactive Elements**: Built with React Three Fiber for engaging visuals
- **Customer Review System**:
  - Submit reviews with star ratings
  - View approved reviews with pagination
  - Moderation system (reviews require approval)
  - Real-time data with Supabase
- **Smooth Animations**: Framer Motion for fluid page transitions
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Glass Morphism Effects**: Modern UI with backdrop blur effects

## Tech Stack

- **Frontend**: Next.js 15.3.2, TypeScript, React 19
- **Styling**: Tailwind CSS 4.1.7, Custom CSS
- **Database**: Supabase (PostgreSQL)
- **Animations**: Framer Motion
- **3D Graphics**: React Three Fiber, Drei
- **State Management**: Zustand
- **Icons**: React Icons

## Getting Started

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd folio
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.local.example .env.local
   ```

   Update `.env.local` with your Supabase credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Set up Supabase Database**

   The database schema is already created. If you need to recreate it:

   ```sql
   -- Create reviews table
   CREATE TABLE reviews (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     reviewer_name VARCHAR(100) NOT NULL,
     reviewer_email VARCHAR(255),
     rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
     review_text TEXT NOT NULL,
     approved BOOLEAN DEFAULT false,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Enable Row Level Security
   ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

   -- Allow public read access to approved reviews
   CREATE POLICY "Allow public read access to approved reviews"
   ON reviews FOR SELECT USING (approved = true);

   -- Allow public insert of new reviews
   CREATE POLICY "Allow public insert of new reviews"
   ON reviews FOR INSERT WITH CHECK (true);
   ```

5. **Run the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
src/
├── app/                 # Next.js app directory
│   ├── globals.css     # Global styles and CSS variables
│   ├── layout.tsx      # Root layout component
│   └── page.tsx        # Main page component
├── components/         # React components
│   ├── ContactSection.tsx
│   ├── ExperienceToolsSection.tsx
│   ├── Footer.tsx
│   ├── HeroSection.tsx
│   ├── Navbar.tsx
│   ├── ReviewsDisplay.tsx      # Display approved reviews
│   ├── ReviewsSection.tsx      # Main reviews section
│   ├── ReviewSubmissionForm.tsx # Review submission form
│   ├── ServicesSection.tsx
│   └── StarRating.tsx          # Star rating component
├── lib/
│   └── supabase.ts     # Supabase client and API functions
└── store/
    └── usePortfolioStore.ts # Zustand state management
```

## Review System Features

### For Visitors
- **Submit Reviews**: Name, optional email, 1-5 star rating, review text
- **View Reviews**: Browse approved reviews with pagination
- **Responsive Design**: Works seamlessly on all devices

### For Administrators
- **Review Moderation**: All reviews start as pending approval
- **Database Access**: Manage reviews through Supabase dashboard
- **Analytics**: Track review metrics and user engagement

## Customization

### Styling
- Update CSS variables in `src/app/globals.css` for color scheme
- Modify Tailwind configuration for design system changes
- Customize animations in component files

### Content
- Update personal information in `src/store/usePortfolioStore.ts`
- Modify section content in respective component files
- Add/remove portfolio sections as needed

## Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically

### Other Platforms
- Ensure environment variables are properly configured
- Build the project: `npm run build`
- Deploy the `out` directory

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
