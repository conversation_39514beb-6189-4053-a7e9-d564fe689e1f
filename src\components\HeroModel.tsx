'use client';

import { useRef, useEffect } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, useGLTF, PerspectiveCamera, Environment, Float } from '@react-three/drei';
import * as THREE from 'three';

// Simple 3D model component
function CubeModel({ color = '#ff5722' }) {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.x = state.clock.getElapsedTime() * 0.2;
      meshRef.current.rotation.y = state.clock.getElapsedTime() * 0.3;
    }
  });

  return (
    <mesh ref={meshRef} castShadow receiveShadow>
      <boxGeometry args={[1, 1, 1]} />
      <meshStandardMaterial color={color} roughness={0.3} metalness={0.7} />
    </mesh>
  );
}

// Developer Workspace Component
function DeveloperWorkspace() {
  const group = useRef<THREE.Group>(null);
  
  useFrame((state) => {
    if (group.current) {
      group.current.rotation.y = Math.sin(state.clock.getElapsedTime() * 0.2) * 0.1;
      group.current.position.y = Math.sin(state.clock.getElapsedTime() * 0.5) * 0.05;
    }
  });

  return (
    <group ref={group} position={[0, -0.5, 0]}>
      {/* Desk */}
      <mesh position={[0, -0.6, 0]} receiveShadow castShadow>
        <boxGeometry args={[3, 0.1, 1.5]} />
        <meshStandardMaterial color="#333333" roughness={0.8} />
      </mesh>
      
      {/* Monitor Stand */}
      <mesh position={[0, -0.25, -0.3]} castShadow>
        <boxGeometry args={[0.2, 0.6, 0.2]} />
        <meshStandardMaterial color="#222222" roughness={0.5} />
      </mesh>
      
      {/* Monitor */}
      <mesh position={[0, 0.3, -0.3]} castShadow>
        <boxGeometry args={[1.8, 1, 0.05]} />
        <meshStandardMaterial color="#111111" roughness={0.4} metalness={0.5} />
      </mesh>
      
      {/* Monitor Screen */}
      <mesh position={[0, 0.3, -0.28]} castShadow>
        <boxGeometry args={[1.7, 0.9, 0.01]} />
        <meshStandardMaterial 
          color="#ff5722" 
          emissive="#ff5722" 
          emissiveIntensity={0.5} 
          roughness={0.2}
        />
      </mesh>
      
      {/* Code on Screen - Vertical Lines */}
      {[...Array(8)].map((_, i) => (
        <mesh key={`code-line-${i}`} position={[-0.7 + (i * 0.2), 0.3, -0.27]} castShadow>
          <boxGeometry args={[0.15, 0.7, 0.005]} />
          <meshStandardMaterial 
            color={i % 3 === 0 ? "#ffffff" : "#aaaaaa"} 
            emissive={i % 3 === 0 ? "#ffffff" : "#aaaaaa"} 
            emissiveIntensity={0.2} 
            transparent={true}
            opacity={0.3}
          />
        </mesh>
      ))}
      
      {/* Keyboard */}
      <mesh position={[0, -0.5, 0.2]} castShadow>
        <boxGeometry args={[1.4, 0.05, 0.5]} />
        <meshStandardMaterial color="#222222" roughness={0.8} />
      </mesh>
      
      {/* Mouse */}
      <group position={[0.8, -0.5, 0.2]} rotation={[Math.PI / 2, 0, 0]}>
        <mesh castShadow>
          <capsuleGeometry args={[0.08, 0.15, 8, 8]} />
          <meshStandardMaterial color="#222222" roughness={0.8} />
        </mesh>
      </group>
      
      {/* Coffee Mug */}
      <group position={[-0.8, -0.4, 0.2]}>
        <mesh castShadow>
          <cylinderGeometry args={[0.1, 0.08, 0.2, 16]} />
          <meshStandardMaterial color="#ff5722" roughness={0.5} />
        </mesh>
        <group position={[0.15, 0, 0]} rotation={[0, Math.PI / 2, 0]}>
          <mesh castShadow>
            <torusGeometry args={[0.05, 0.02, 8, 16, Math.PI]} />
            <meshStandardMaterial color="#ff5722" roughness={0.5} />
          </mesh>
        </group>
      </group>
      
      {/* Plant */}
      <group position={[1.2, -0.3, -0.3]}>
        <mesh castShadow>
          <cylinderGeometry args={[0.1, 0.12, 0.2, 16]} />
          <meshStandardMaterial color="#ff5722" roughness={0.8} />
        </mesh>
        <mesh position={[0, 0.2, 0]} castShadow>
          <sphereGeometry args={[0.15, 16, 16]} />
          <meshStandardMaterial color="#4CAF50" roughness={0.8} />
        </mesh>
      </group>
    </group>
  );
}

// Floating cubes
function FloatingCubes() {
  const cubes = [
    { position: [1.5, 1, -2], size: 0.3, color: '#ff5722', speed: 1 },
    { position: [-1.5, -1, -1], size: 0.2, color: '#ff8a65', speed: 1.5 },
    { position: [1, -1.5, -3], size: 0.25, color: '#e64a19', speed: 0.7 },
    { position: [-2, 1.5, -2], size: 0.15, color: '#ff5722', speed: 1.2 },
  ];
  
  return (
    <>
      {cubes.map((cube, index) => (
        <Float key={index} speed={cube.speed} rotationIntensity={1} floatIntensity={2}>
          <mesh position={cube.position as [number, number, number]} castShadow>
            <boxGeometry args={[cube.size, cube.size, cube.size]} />
            <meshStandardMaterial color={cube.color} roughness={0.3} metalness={0.7} />
          </mesh>
        </Float>
      ))}
    </>
  );
}

export default function HeroModel() {
  return (
    <Canvas
      shadows
      dpr={[1, 2]}
      gl={{ 
        antialias: true, 
        alpha: true
      }}
      camera={{ position: [0, 0, 5], fov: 40 }}
      style={{
        width: '100%',
        height: '100%',
        position: 'absolute',
        top: 0,
        left: 0,
        outline: 'none',
        backgroundColor: 'transparent'
      }}
    >
      {/* Setting the background to be fully transparent */}
      <scene background={null} />
      <ambientLight intensity={0.5} />
      <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} castShadow />
      <pointLight position={[-10, -10, -10]} intensity={0.5} />
      
      <DeveloperWorkspace />
      <FloatingCubes />
      
      <Environment preset="city" />
      <OrbitControls 
        enableZoom={false} 
        enablePan={false} 
        rotateSpeed={0.5} 
        minPolarAngle={Math.PI / 4}
        maxPolarAngle={Math.PI / 2}
      />
    </Canvas>
  );
}
