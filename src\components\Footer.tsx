'use client';

import { motion } from 'framer-motion';
import { usePortfolioStore } from '@/store/usePortfolioStore';

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { name } = usePortfolioStore();

  // Extract first and last name for logo
  const nameParts = name.split(' ');
  const firstName = nameParts[0];
  const lastName = nameParts.slice(1).join(' ');

  return (
    <footer className="py-8 border-t border-border">
      <div className="container mx-auto px-4 md:px-6">
        <motion.div
          className="text-center text-muted-foreground"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
        >
          <p>
            © {currentYear}. Made with passion by{' '}
            <span className="text-foreground font-medium">
              {firstName} {lastName}
            </span>
            .<br />
            All right reserved.
          </p>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
