'use client';

import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { usePortfolioStore } from '@/store/usePortfolioStore';
import { FaEnvelope, FaPhone, FaMapMarkerAlt, FaFile, FaPaperPlane } from 'react-icons/fa';

const ContactSection = () => {
  const { isContactFormSubmitted, setContactFormSubmitted } = usePortfolioStore();
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, margin: '-100px' });
  
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    file: null as File | null
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Enhanced Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0, scale: 0.95 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  const formVariants = {
    hidden: { opacity: 0, x: 50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFormData(prev => ({ ...prev, file: e.target.files![0] }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    setContactFormSubmitted(true);
    setIsSubmitting(false);
    setFormData({
      name: '',
      email: '',
      subject: '',
      message: '',
      file: null
    });
  };

  return (
    <section 
      id="contact" 
      ref={sectionRef}
      className="py-24 relative overflow-hidden"
      style={{ background: 'var(--card-bg)' }}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 opacity-30">
        <div className="absolute top-0 left-0 w-full h-full bg-[var(--background)] opacity-90"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 rounded-full bg-[var(--accent-primary)] filter blur-[150px] opacity-10 animate-pulse"></div>
        <div className="absolute top-0 left-0 w-80 h-80 rounded-full bg-[var(--accent-secondary)] filter blur-[120px] opacity-10 animate-pulse delay-1000"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4 inline-block relative">
              <span className="text-foreground relative z-10">Get In Touch</span>
              <span className="absolute bottom-0 left-0 w-full h-3 bg-[var(--accent-tertiary)] opacity-20 rounded"></span>
            </h2>
          </motion.div>
          <motion.p 
            className="text-foreground/70 max-w-2xl mx-auto text-lg"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Let's discuss your project and create something amazing together
          </motion.p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          {/* Left Side - Contact Info */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            className="relative"
          >
            <div className="relative z-10">
              <motion.h3 variants={itemVariants} className="text-2xl font-bold mb-6 text-foreground inline-flex items-center">
                <span className="w-12 h-12 rounded-xl glass flex items-center justify-center mr-4 neon-box">
                  <FaPaperPlane className="text-[var(--accent-primary)] text-xl" />
                </span>
                <span>Let's Connect</span>
              </motion.h3>
              <motion.p variants={itemVariants} className="text-foreground/70 mb-8 max-w-md">
                Ready to bring your digital vision to life? Let's collaborate on your next project. 
                Fill out the form, and I'll get back to you promptly to discuss how we can create something amazing together.
              </motion.p>
              
              <div className="space-y-6">
                <motion.div variants={itemVariants} className="flex items-center group">
                  <div className="w-12 h-12 rounded-lg glass flex items-center justify-center mr-4 group-hover:neon-box transition-all">
                    <FaEnvelope className="text-[var(--accent-primary)]" />
                  </div>
                  <div>
                    <p className="text-foreground/50 text-sm">Email</p>
                    <p className="text-foreground group-hover:text-[var(--accent-primary)] transition-colors"><EMAIL></p>
                  </div>
                </motion.div>
                
                <motion.div variants={itemVariants} className="flex items-center group">
                  <div className="w-12 h-12 rounded-lg glass flex items-center justify-center mr-4 group-hover:neon-box transition-all">
                    <FaPhone className="text-[var(--accent-secondary)]" />
                  </div>
                  <div>
                    <p className="text-foreground/50 text-sm">Phone</p>
                    <p className="text-foreground group-hover:text-[var(--accent-secondary)] transition-colors">+****************</p>
                  </div>
                </motion.div>
                
                <motion.div variants={itemVariants} className="flex items-center group">
                  <div className="w-12 h-12 rounded-lg glass flex items-center justify-center mr-4 group-hover:neon-box transition-all">
                    <FaMapMarkerAlt className="text-[var(--accent-tertiary)]" />
                  </div>
                  <div>
                    <p className="text-foreground/50 text-sm">Location</p>
                    <p className="text-foreground group-hover:text-[var(--accent-tertiary)] transition-colors">San Francisco, CA</p>
                  </div>
                </motion.div>
              </div>
              
              {/* Social links */}
              <motion.div variants={itemVariants} className="mt-10">
                <p className="text-foreground/50 text-sm mb-3">Connect with me</p>
                <div className="flex space-x-4">
                  {['#06b6d4', '#8b5cf6', '#10b981', '#3b82f6'].map((color, index) => (
                    <motion.a 
                      key={index}
                      href="#"
                      className="w-10 h-10 rounded-lg glass flex items-center justify-center hover:neon-box transition-all"
                      style={{ color }}
                      whileHover={{ y: -5 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <span className="text-lg">{index === 0 ? <FaEnvelope /> : index === 1 ? <FaPhone /> : <FaMapMarkerAlt />}</span>
                    </motion.a>
                  ))}
                </div>
              </motion.div>
            </div>
            
            {/* Background elements */}
            <div className="absolute top-1/2 left-0 transform -translate-y-1/2 -translate-x-1/2 w-64 h-64 rounded-full bg-[var(--accent-primary)] filter blur-[100px] opacity-10"></div>
          </motion.div>

          {/* Right Side - Enhanced Contact Form */}
          <motion.div
            variants={formVariants}
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
          >
            {isContactFormSubmitted ? (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                className="bg-gray-800 rounded-lg p-8 text-center"
              >
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-r from-green-400 to-green-500 flex items-center justify-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <h3 className="text-2xl font-bold mb-2">Thank You!</h3>
                <p className="text-gray-300 mb-6">Your message has been sent successfully. I'll get back to you soon!</p>
                <button
                  onClick={() => setContactFormSubmitted(false)}
                  className="bg-gradient-to-r from-orange-500 to-pink-500 text-white px-6 py-3 rounded-full hover:shadow-lg transition-all"
                >
                  Send Another Message
                </button>
              </motion.div>
            ) : (
              <motion.form
                onSubmit={handleSubmit}
                className="glass rounded-xl p-6 md:p-8 border border-[rgba(255,255,255,0.1)]"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
              >
                <div className="grid grid-cols-1 gap-6">
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    <label htmlFor="name" className="block text-foreground/70 mb-2 font-medium">Name</label>
                    <motion.input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full glass border border-[rgba(255,255,255,0.1)] rounded-lg px-4 py-3 text-foreground focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent transition-all"
                      placeholder="Your name"
                      whileFocus={{ scale: 1.02 }}
                    />
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <label htmlFor="email" className="block text-foreground/70 mb-2 font-medium">Email</label>
                    <motion.input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full glass border border-[rgba(255,255,255,0.1)] rounded-lg px-4 py-3 text-foreground focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent transition-all"
                      placeholder="Your email"
                      whileFocus={{ scale: 1.02 }}
                    />
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 }}
                  >
                    <label htmlFor="subject" className="block text-foreground/70 mb-2 font-medium">Subject</label>
                    <motion.input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      className="w-full glass border border-[rgba(255,255,255,0.1)] rounded-lg px-4 py-3 text-foreground focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent transition-all"
                      placeholder="Subject"
                      whileFocus={{ scale: 1.02 }}
                    />
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.4 }}
                  >
                    <label htmlFor="message" className="block text-foreground/70 mb-2 font-medium">Message</label>
                    <motion.textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={4}
                      className="w-full glass border border-[rgba(255,255,255,0.1)] rounded-lg px-4 py-3 text-foreground focus:outline-none focus:ring-2 focus:ring-[var(--accent-primary)] focus:border-transparent transition-all resize-none"
                      placeholder="Your message"
                      whileFocus={{ scale: 1.02 }}
                    />
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 }}
                  >
                    <label htmlFor="file" className="block text-foreground/70 mb-2 font-medium">Attachment (Optional)</label>
                    <div className="relative">
                      <input
                        type="file"
                        id="file"
                        onChange={handleFileChange}
                        className="hidden"
                      />
                      <motion.label
                        htmlFor="file"
                        className="flex items-center justify-center w-full glass border border-dashed border-[rgba(255,255,255,0.2)] rounded-lg px-4 py-3 text-foreground/70 cursor-pointer transition-all"
                        whileHover={{
                          scale: 1.02,
                          borderColor: "rgba(255, 87, 34, 0.5)",
                          backgroundColor: "rgba(255, 87, 34, 0.05)"
                        }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <FaFile className="mr-2" />
                        {formData.file ? formData.file.name : 'Choose a file'}
                      </motion.label>
                    </div>
                  </motion.div>

                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6 }}
                  >
                    <motion.button
                      type="submit"
                      disabled={isSubmitting}
                      className="w-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white px-6 py-3 rounded-lg font-medium flex items-center justify-center transition-all"
                      whileHover={{
                        scale: 1.02,
                        boxShadow: "0 10px 30px rgba(255, 87, 34, 0.3)"
                      }}
                      whileTap={{ scale: 0.98 }}
                      animate={isSubmitting ? { opacity: 0.7 } : { opacity: 1 }}
                    >
                      {isSubmitting ? (
                        <>
                          <motion.svg
                            className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                            xmlns="http://www.w3.org/2000/svg"
                            fill="none"
                            viewBox="0 0 24 24"
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          >
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </motion.svg>
                          Sending...
                        </>
                      ) : (
                        <>
                          <FaPaperPlane className="mr-2" />
                          Send Message
                        </>
                      )}
                    </motion.button>
                  </motion.div>
                </div>
              </motion.form>
            )}
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ContactSection;
