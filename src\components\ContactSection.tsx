'use client';

import { useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { usePortfolioStore } from '@/store/usePortfolioStore';
import { FaEnvelope, FaLinkedin, FaGithub, FaInstagram, FaFacebook } from 'react-icons/fa';

const ContactSection = () => {
  const { email, availability } = usePortfolioStore();
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, margin: '-100px' });
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="contact" ref={sectionRef} className="py-20 relative">
      <div className="container mx-auto px-4 md:px-6">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold mb-4">
            <span className="text-foreground">Get In </span>
            <span className="text-primary">Touch</span>
          </h2>
          <div className="w-24 h-1 bg-primary mx-auto mb-6"></div>
        </motion.div>

        {/* Availability Status */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="text-center mb-16"
        >
          <motion.div
            variants={itemVariants}
            className="inline-flex items-center gap-3 glass rounded-full px-6 py-3 border border-border"
          >
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-foreground font-medium">{availability}</span>
          </motion.div>
        </motion.div>

        {/* Contact Content - Reference Layout */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-4xl mx-auto text-center"
        >
          <motion.p
            variants={itemVariants}
            className="text-lg text-muted-foreground mb-12 leading-relaxed"
          >
            Have an exciting project you need help with?<br />
            Send me an email or contact me via instant message!
          </motion.p>

          {/* Email - Large and Prominent */}
          <motion.div variants={itemVariants} className="mb-12">
            <motion.a
              href={`mailto:${email}`}
              className="text-3xl md:text-4xl font-bold text-foreground hover:text-primary transition-colors block"
              whileHover={{ scale: 1.02 }}
            >
              {email}
            </motion.a>
          </motion.div>

          {/* Social Links - Reference Style */}
          <motion.div variants={itemVariants}>
            <div className="flex flex-wrap justify-center gap-8 text-muted-foreground">
              <motion.a
                href="https://m.me/josephlawrence"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-primary transition-colors underline"
                whileHover={{ y: -2 }}
              >
                Messenger
              </motion.a>

              <motion.a
                href="https://www.linkedin.com/in/josephlawrence"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-primary transition-colors underline"
                whileHover={{ y: -2 }}
              >
                LinkedIn
              </motion.a>

              <motion.a
                href="https://www.instagram.com/josephlawrence"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-primary transition-colors underline"
                whileHover={{ y: -2 }}
              >
                Instagram
              </motion.a>

              <motion.a
                href="https://github.com/josephlawrence"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-primary transition-colors underline"
                whileHover={{ y: -2 }}
              >
                Github
              </motion.a>
            </div>
          </motion.div>
        </motion.div>

      </div>
    </section>
  );
};

export default ContactSection;
