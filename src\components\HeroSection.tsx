'use client';

import { useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import Image from 'next/image';
import { usePortfolioStore } from '@/store/usePortfolioStore';
import { FaChevronDown } from 'react-icons/fa';
import dynamic from 'next/dynamic';

// Import the 3D model component with dynamic loading to avoid SSR issues
const HeroModel = dynamic(() => import('./HeroModel'), {
  ssr: false,
  loading: () => <div className="w-full h-full flex items-center justify-center"><div className="w-20 h-20 border-4 border-t-primary border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div></div>
});

const HeroSection = () => {
  const { name, role, featuredIn } = usePortfolioStore();
  const controls = useAnimation();
  const ref = useRef(null);
  const inView = useInView(ref, { once: false });

  // Split the name to highlight the last name
  const nameParts = name.split(' ');
  const firstName = nameParts[0];
  const lastName = nameParts.slice(1).join(' ');

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  // Enhanced text animation variants
  const textVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.9 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        delay: i * 0.15,
        duration: 0.8,
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    })
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  // Enhanced floating animation for the profile container
  const floatingAnimation = {
    y: [0, -20, 0],
    rotate: [0, 2, 0],
    transition: {
      duration: 8,
      repeat: Infinity,
      repeatType: 'reverse' as const,
      ease: "easeInOut"
    }
  };

  // Stagger animation for background elements
  const backgroundVariants = {
    hidden: { opacity: 0, scale: 0 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const backgroundItemVariants = {
    hidden: { opacity: 0, scale: 0, rotate: 0 },
    visible: {
      opacity: [0.6, 0.8, 0.6],
      scale: 1,
      rotate: 360,
      transition: {
        duration: 2,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="hero-section" ref={ref} className="min-h-screen flex items-center pt-24 pb-16 relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-background"></div>

      {/* Floating elements */}
      <motion.div
        className="absolute top-20 right-20 w-16 h-16 bg-primary/20 rounded-lg"
        animate={{
          y: [0, -30, 0],
          rotate: [12, 25, 12],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />

      <motion.div
        className="absolute bottom-40 left-20 w-12 h-12 bg-accent/20 rounded-lg"
        animate={{
          y: [0, -25, 0],
          rotate: [-12, -25, -12],
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 1
        }}
      />

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        <div className="text-center">
          {/* Name and Title */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate={inView ? "visible" : "hidden"}
            className="mb-8"
          >
            <motion.h1
              variants={itemVariants}
              className="text-5xl md:text-7xl font-bold mb-4"
            >
              <span className="text-foreground">{firstName}</span>
              <span className="text-primary ml-2">{lastName}</span>
            </motion.h1>

            <motion.h2
              variants={itemVariants}
              className="text-xl md:text-2xl text-muted-foreground mb-8"
            >
              {role}
            </motion.h2>
          </motion.div>

          {/* Featured In Section */}
          <motion.div
            variants={itemVariants}
            className="mb-16"
          >
            <p className="text-sm text-muted-foreground mb-6">As featured in</p>
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-60">
              {featuredIn.map((publication, index) => (
                <motion.div
                  key={publication}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 0.6, y: 0 }}
                  transition={{ delay: index * 0.1 + 0.5 }}
                  className="text-sm font-medium text-muted-foreground hover:text-foreground transition-colors cursor-pointer"
                >
                  {publication}
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Scroll Down Indicator */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.5 }}
            className="flex flex-col items-center"
          >
            <motion.a
              href="#expertise"
              className="flex flex-col items-center text-muted-foreground hover:text-primary transition-colors group"
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
            >
              <FaChevronDown className="w-6 h-6 group-hover:scale-110 transition-transform" />
            </motion.a>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
