'use client';

import { useEffect, useRef } from 'react';
import { motion, useAnimation, useInView } from 'framer-motion';
import Image from 'next/image';
import { usePortfolioStore } from '@/store/usePortfolioStore';
import dynamic from 'next/dynamic';

// Import the 3D model component with dynamic loading to avoid SSR issues
const HeroModel = dynamic(() => import('./HeroModel'), {
  ssr: false,
  loading: () => <div className="w-full h-full flex items-center justify-center"><div className="w-20 h-20 border-4 border-t-[var(--accent-primary)] border-r-transparent border-b-transparent border-l-transparent rounded-full animate-spin"></div></div>
});

const HeroSection = () => {
  const { name, role } = usePortfolioStore();
  const controls = useAnimation();
  const ref = useRef(null);
  const inView = useInView(ref, { once: false });
  
  // Split the name to highlight the last name
  const nameParts = name.split(' ');
  const firstName = nameParts[0];
  const lastName = nameParts.slice(1).join(' ');

  useEffect(() => {
    if (inView) {
      controls.start('visible');
    }
  }, [controls, inView]);

  // Enhanced text animation variants
  const textVariants = {
    hidden: { opacity: 0, y: 30, scale: 0.9 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      scale: 1,
      transition: {
        delay: i * 0.15,
        duration: 0.8,
        type: "spring",
        stiffness: 100,
        damping: 10
      }
    })
  };

  // Enhanced floating animation for the profile container
  const floatingAnimation = {
    y: [0, -20, 0],
    rotate: [0, 2, 0],
    transition: {
      duration: 8,
      repeat: Infinity,
      repeatType: 'reverse' as const,
      ease: "easeInOut"
    }
  };

  // Stagger animation for background elements
  const backgroundVariants = {
    hidden: { opacity: 0, scale: 0 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3
      }
    }
  };

  const backgroundItemVariants = {
    hidden: { opacity: 0, scale: 0, rotate: 0 },
    visible: {
      opacity: [0.6, 0.8, 0.6],
      scale: 1,
      rotate: 360,
      transition: {
        duration: 2,
        ease: "easeOut"
      }
    }
  };

  return (
    <section ref={ref} className="min-h-screen flex items-center pt-24 pb-16 relative overflow-hidden">
      {/* Enhanced Background elements */}
      <motion.div
        className="absolute inset-0 -z-10"
        variants={backgroundVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="absolute top-0 left-0 w-full h-full bg-[var(--background)] opacity-95"></div>

        {/* Enhanced floating cubes with motion */}
        <motion.div
          className="absolute top-20 right-20 w-16 h-16 bg-[var(--accent-primary)] opacity-80 rounded-lg"
          variants={backgroundItemVariants}
          animate={{
            y: [0, -30, 0],
            rotate: [12, 25, 12],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute bottom-40 left-20 w-12 h-12 bg-[var(--accent-secondary)] opacity-70 rounded-lg"
          variants={backgroundItemVariants}
          animate={{
            y: [0, -25, 0],
            rotate: [-12, -25, -12],
            scale: [1, 1.2, 1]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        />

        <motion.div
          className="absolute bottom-20 right-1/4 w-10 h-10 bg-[var(--accent-tertiary)] opacity-80 rounded-lg"
          variants={backgroundItemVariants}
          animate={{
            y: [0, -20, 0],
            rotate: [45, 60, 45],
            scale: [1, 1.15, 1]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />

        <motion.div
          className="absolute top-32 right-1/3 w-14 h-14 bg-[#444444] opacity-50 rounded-lg"
          variants={backgroundItemVariants}
          animate={{
            y: [0, -35, 0],
            rotate: [12, 30, 12],
            scale: [1, 1.1, 1]
          }}
          transition={{
            duration: 7,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 0.5
          }}
        />

        {/* Enhanced glow effects */}
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 rounded-full bg-[var(--accent-primary)] filter blur-[100px] opacity-15"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.15, 0.25, 0.15]
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />

        <motion.div
          className="absolute bottom-1/4 right-1/4 w-80 h-80 rounded-full bg-[var(--accent-secondary)] filter blur-[120px] opacity-15"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.15, 0.3, 0.15]
          }}
          transition={{
            duration: 10,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
        />
      </motion.div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        {/* Social Media Icons on Left Side */}
        <div className="fixed left-8 top-1/2 transform -translate-y-1/2 hidden md:flex flex-col space-y-6 z-20">
          <motion.a 
            href="#" 
            className="text-foreground/80 hover:text-[var(--accent-primary)] transition-colors"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path><line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line></svg>
          </motion.a>
          <motion.a 
            href="#" 
            className="text-foreground/80 hover:text-[var(--accent-primary)] transition-colors"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path></svg>
          </motion.a>
          <motion.a 
            href="#" 
            className="text-foreground/80 hover:text-[var(--accent-primary)] transition-colors"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path></svg>
          </motion.a>
          <motion.a 
            href="#" 
            className="text-foreground/80 hover:text-[var(--accent-primary)] transition-colors"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.5 }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M22.54 6.42a2.78 2.78 0 0 0-1.94-2C18.88 4 12 4 12 4s-6.88 0-8.6.46a2.78 2.78 0 0 0-1.94 2A29 29 0 0 0 1 11.75a29 29 0 0 0 .46 5.33A2.78 2.78 0 0 0 3.4 19c1.72.46 8.6.46 8.6.46s6.88 0 8.6-.46a2.78 2.78 0 0 0 1.94-2 29 29 0 0 0 .46-5.25 29 29 0 0 0-.46-5.33z"></path><polygon points="9.75 15.02 15.5 11.75 9.75 8.48 9.75 15.02"></polygon></svg>
          </motion.a>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          {/* Main Content */}
          <div className="order-2 md:order-1 space-y-6">
            <motion.div
              initial="hidden"
              animate={controls}
              custom={0}
              variants={textVariants}
            >
              <div className="flex items-center space-x-2">
                <span className="inline-block w-6 h-0.5 bg-[var(--accent-primary)]"></span>
                <h2 className="text-xl text-foreground/80 font-light tracking-wider">Hello</h2>
              </div>
            </motion.div>
            
            <motion.div
              initial="hidden"
              animate={controls}
              custom={1}
              variants={textVariants}
            >
              <h1 className="text-5xl md:text-7xl font-bold mb-4 tracking-tight">
                <span className="text-foreground block">I'm </span>
                <span className="text-foreground block">
                  {name}
                </span>
              </h1>
            </motion.div>
            
            <motion.div
              initial="hidden"
              animate={controls}
              custom={2}
              variants={textVariants}
            >
              <p className="text-xl text-foreground/80 mb-8 font-light tracking-wide">
                {role}
              </p>
            </motion.div>
            
            <motion.div
              initial="hidden"
              animate={controls}
              custom={3}
              variants={textVariants}
            >
              <motion.a
                href="#contact"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-[var(--accent-primary)] text-white px-8 py-3 rounded-md text-base font-medium inline-block hover:bg-[var(--accent-tertiary)] transition-all"
              >
                Learn more
              </motion.a>
            </motion.div>
          </div>

          {/* Right Side - Enhanced 3D Character */}
          <motion.div
            className="order-1 md:order-2 flex justify-center items-center relative"
            initial={{ opacity: 0, scale: 0.8, rotateY: -30 }}
            animate={{ opacity: 1, scale: 1, rotateY: 0 }}
            transition={{ duration: 1, type: "spring", stiffness: 80, damping: 20 }}
          >
            <motion.div
              className="relative"
              animate={floatingAnimation}
            >
              {/* 3D Character Container with enhanced styling */}
              <motion.div
                className="relative w-80 h-80 md:w-[500px] md:h-[500px] rounded-2xl overflow-visible"
                style={{ position: 'relative' }}
                whileHover={{
                  scale: 1.05,
                  rotateY: 5,
                  transition: { duration: 0.3 }
                }}
              >
                {/* Glowing border effect */}
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-[var(--accent-primary)] via-[var(--accent-secondary)] to-[var(--accent-tertiary)] opacity-20 blur-xl"></div>

                <HeroModel />

                {/* Enhanced Emoji badge */}
                <motion.div
                  initial={{ opacity: 0, scale: 0, rotate: -20 }}
                  animate={{ opacity: 1, scale: 1, rotate: 0 }}
                  transition={{ delay: 1.2, type: "spring", stiffness: 200 }}
                  className="absolute top-5 right-5 glass rounded-full p-3 shadow-2xl z-10 border border-[rgba(255,255,255,0.2)]"
                  whileHover={{
                    scale: 1.1,
                    rotate: 10,
                    transition: { duration: 0.2 }
                  }}
                  animate={{
                    y: [0, -5, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  <span className="text-2xl">😎</span>
                </motion.div>

                {/* Additional floating elements */}
                <motion.div
                  className="absolute -top-2 -left-2 w-4 h-4 bg-[var(--accent-primary)] rounded-full opacity-60"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.6, 1, 0.6]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                />

                <motion.div
                  className="absolute -bottom-3 -right-3 w-6 h-6 bg-[var(--accent-secondary)] rounded-full opacity-50"
                  animate={{
                    scale: [1.2, 1, 1.2],
                    opacity: [0.5, 0.8, 0.5]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                />
              </motion.div>
            </motion.div>
          </motion.div>
          
          {/* Scroll down indicator */}
          <motion.div
            className="absolute bottom-8 left-1/2 transform -translate-x-1/2 hidden md:flex flex-col items-center text-foreground/60"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.5 }}
          >
            <span className="text-xs mb-2">scroll down</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="animate-bounce">
              <path d="M12 5v14M19 12l-7 7-7-7"/>
            </svg>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
