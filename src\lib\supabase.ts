import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://tlehdbqdiqilpparmhkf.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Types for our reviews table
export interface Review {
  id: string;
  reviewer_name: string;
  reviewer_email?: string;
  rating: number;
  review_text: string;
  approved: boolean;
  created_at: string;
  updated_at: string;
}

export interface NewReview {
  reviewer_name: string;
  reviewer_email?: string;
  rating: number;
  review_text: string;
}

// Mock data for fallback
const mockReviews: Review[] = [
  {
    id: 'mock-1',
    reviewer_name: '<PERSON>',
    reviewer_email: '<EMAIL>',
    rating: 5,
    review_text: 'Absolutely fantastic work! The website exceeded all my expectations. Professional, responsive, and delivered on time.',
    approved: true,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z'
  },
  {
    id: 'mock-2',
    reviewer_name: '<PERSON>',
    reviewer_email: '<EMAIL>',
    rating: 5,
    review_text: 'Outstanding developer! Created exactly what I envisioned and more. Great communication throughout the project.',
    approved: true,
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-10T10:00:00Z'
  },
  {
    id: 'mock-3',
    reviewer_name: 'Emily Rodriguez',
    reviewer_email: '<EMAIL>',
    rating: 4,
    review_text: 'Very pleased with the final result. Clean code, modern design, and excellent attention to detail.',
    approved: true,
    created_at: '2024-01-05T10:00:00Z',
    updated_at: '2024-01-05T10:00:00Z'
  }
];

// API functions for reviews
export const reviewsApi = {
  // Submit a new review
  async submitReview(review: NewReview): Promise<{ data: Review | null; error: any }> {
    try {
      const { data, error } = await supabase
        .from('reviews')
        .insert([{
          ...review,
          approved: false // Ensure new reviews are not approved by default
        }])
        .select()
        .single();

      if (error) {
        console.error('Supabase error:', error);
        return { data: null, error: error.message || 'Failed to submit review' };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Submit review error:', error);
      return { data: null, error: 'Failed to submit review. Please try again.' };
    }
  },

  // Get approved reviews with pagination
  async getApprovedReviews(page = 0, limit = 6): Promise<{ data: Review[] | null; error: any; count?: number }> {
    try {
      const { data, error, count } = await supabase
        .from('reviews')
        .select('*', { count: 'exact' })
        .eq('approved', true)
        .order('created_at', { ascending: false })
        .range(page * limit, (page + 1) * limit - 1);

      if (error) {
        console.error('Supabase error:', error);
        // Return mock data as fallback
        console.log('Using mock data as fallback');
        const startIndex = page * limit;
        const endIndex = startIndex + limit;
        const paginatedMockData = mockReviews.slice(startIndex, endIndex);
        return {
          data: paginatedMockData,
          error: null,
          count: mockReviews.length
        };
      }

      return { data: data || [], error: null, count: count || 0 };
    } catch (error) {
      console.error('Get reviews error:', error);
      // Return mock data as fallback
      console.log('Using mock data as fallback due to exception');
      const startIndex = page * limit;
      const endIndex = startIndex + limit;
      const paginatedMockData = mockReviews.slice(startIndex, endIndex);
      return {
        data: paginatedMockData,
        error: null,
        count: mockReviews.length
      };
    }
  }
};
