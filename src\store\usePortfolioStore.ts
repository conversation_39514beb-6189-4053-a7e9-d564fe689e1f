import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type Theme = 'light' | 'dark';

interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  category: string;
  technologies: string[];
  liveUrl?: string;
  githubUrl?: string;
  featured?: boolean;
}

interface Experience {
  id: string;
  company: string;
  position: string;
  duration: string;
  location: string;
  website?: string;
  description: string;
  technologies: string[];
  logo?: string;
}

interface Expertise {
  title: string;
  description: string;
  icon: string;
}

interface PortfolioState {
  // Theme
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;

  // Personal Info
  name: string;
  role: string;
  title: string;
  bio: string;
  email: string;
  location: string;
  availability: string;

  // Portfolio Data
  expertise: Expertise[];
  projects: Project[];
  experience: Experience[];
  featuredIn: string[];

  // Form States
  isContactFormSubmitted: boolean;
  isReviewFormSubmitted: boolean;

  // Actions
  setName: (name: string) => void;
  setRole: (role: string) => void;
  setContactFormSubmitted: (isSubmitted: boolean) => void;
  setReviewFormSubmitted: (isSubmitted: boolean) => void;
}

export const usePortfolioStore = create<PortfolioState>()(
  persist(
    (set, get) => ({
      // Theme
      theme: 'dark',
      setTheme: (theme) => {
        set({ theme });
        if (typeof window !== 'undefined') {
          document.documentElement.setAttribute('data-theme', theme);
        }
      },
      toggleTheme: () => {
        const newTheme = get().theme === 'light' ? 'dark' : 'light';
        get().setTheme(newTheme);
      },

      // Personal Info
      name: 'Tamal Sen',
      role: 'Software Engineer, Front end & App Developer.',
      title: 'Software Engineer',
      bio: 'Passionate about creating exceptional digital experiences with modern technologies.',
      email: '<EMAIL>',
      location: 'Dhaka, Bangladesh',
      availability: 'Available for select freelance opportunities',

      // Portfolio Data
      expertise: [
        {
          title: 'Software Development',
          description: 'Experienced in both functional and OOP: JavaScript, TypeScript, Python, Java.',
          icon: 'code'
        },
        {
          title: 'Frontend Development',
          description: 'Passionate about UI/UX. Over 5 years of development experience in React, Next.js, and modern frameworks.',
          icon: 'react'
        },
        {
          title: 'Mobile Development',
          description: 'Skilled in developing cross-platform mobile applications using React Native and modern tools.',
          icon: 'mobile'
        }
      ],

      projects: [
        {
          id: '1',
          title: 'Tryotel App',
          description: 'Cross-platform travel booking application with seamless user experience and real-time booking capabilities',
          image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop&crop=center',
          category: 'Mobile Development',
          technologies: ['Flutter', 'Dart', 'Firebase', 'REST API'],
          liveUrl: 'https://tryotel.com',
          githubUrl: 'https://github.com',
          featured: true
        },
        {
          id: '2',
          title: 'Flight Local (B2B Travel Solution)',
          description: 'Comprehensive B2B travel management platform for agencies and corporate clients',
          image: 'https://images.unsplash.com/photo-1436491865332-7a61a109cc05?w=800&h=600&fit=crop&crop=center',
          category: 'Web Development',
          technologies: ['React', 'Next.js', 'TypeScript', 'Node.js'],
          liveUrl: 'https://flightlocal.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '3',
          title: 'AI Lab Granada',
          description: 'Modern website for AI research laboratory showcasing cutting-edge projects and research',
          image: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=600&fit=crop&crop=center',
          category: 'Web Development',
          technologies: ['React', 'Next.js', 'Tailwind CSS', 'Framer Motion'],
          liveUrl: 'https://ailabgranada.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '4',
          title: 'Khora – Urban Thinkers',
          description: 'Professional consulting firm website with modern design and interactive elements',
          image: 'https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=800&h=600&fit=crop&crop=center',
          category: 'Web Development',
          technologies: ['WordPress', 'PHP', 'JavaScript', 'CSS3'],
          liveUrl: 'https://khora.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '5',
          title: 'Tapy – Download. Connect. Unlock.',
          description: 'Innovative mobile app platform for seamless connectivity and digital experiences',
          image: 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=800&h=600&fit=crop&crop=center',
          category: 'Mobile Development',
          technologies: ['React Native', 'TypeScript', 'Firebase', 'Redux'],
          liveUrl: 'https://tapy.co',
          githubUrl: 'https://github.com'
        },
        {
          id: '6',
          title: 'Walker IP Pty Ltd',
          description: 'Professional intellectual property law firm website with clean, corporate design',
          image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop&crop=center',
          category: 'Web Development',
          technologies: ['WordPress', 'PHP', 'JavaScript', 'SCSS'],
          liveUrl: 'https://walkerip.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '7',
          title: 'Tryotel Web (B2C)',
          description: 'Consumer-facing travel booking website with intuitive design and powerful search capabilities',
          image: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800&h=600&fit=crop&crop=center',
          category: 'Web Development',
          technologies: ['React', 'Next.js', 'TypeScript', 'Stripe API'],
          liveUrl: 'https://tryotel.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '8',
          title: 'Kananaskis Nordic Spa',
          description: 'Luxury spa website featuring elegant design and online booking system',
          image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=800&h=600&fit=crop&crop=center',
          category: 'Web Development',
          technologies: ['WordPress', 'PHP', 'JavaScript', 'WooCommerce'],
          liveUrl: 'https://kananaskisnordicspa.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '9',
          title: 'A Higher Thought',
          description: 'Mindfulness and wellness platform with interactive content and user engagement features',
          image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop&crop=center',
          category: 'Web Development',
          technologies: ['React', 'Node.js', 'MongoDB', 'Express'],
          liveUrl: 'https://ahigherthought.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '10',
          title: 'All the roads of Chittagong',
          description: 'Interactive data visualization project mapping the road network of Chittagong city',
          image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop&crop=center',
          category: 'Data Visualization',
          technologies: ['D3.js', 'JavaScript', 'Python', 'Mapbox'],
          liveUrl: 'https://chittagongroads.com',
          githubUrl: 'https://github.com'
        }
      ],

      experience: [
        {
          id: '1',
          company: 'Life Coach Elevate',
          position: 'Co-Founder',
          duration: '2024 - Present',
          location: 'Arizona, USA',
          website: 'https://lifecoachelevate.com',
          description: 'Co-founded Life Coach Elevate, managing end-to-end technical infrastructure, including server architecture, automation pipeline development, leadership of the web development and design team, and driving initiatives to optimize scalability and system performance.',
          technologies: ['DevOps', 'CI/CD', 'Kubernetes', 'JS/TS', 'NextJS'],
          logo: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=100&h=100&fit=crop&crop=center'
        },
        {
          id: '2',
          company: 'Saimon Global Ltd',
          position: 'Senior Lead Software Engineer',
          duration: '2019 - 2024',
          location: 'Dhaka, Bangladesh',
          website: 'https://saimonglobal.com',
          description: 'Led a frontend team to design and develop robust B2C and B2B Travel Tech solutions, utilizing React/Next.js for web applications and Flutter SDK for cross-platform mobile apps, with a focus on responsive design, scalability, and enhanced user experience.',
          technologies: ['JS', 'TS', 'Dart', 'React', 'NextJS', 'Flutter'],
          logo: 'https://images.unsplash.com/photo-1549923746-c502d488b3ea?w=100&h=100&fit=crop&crop=center'
        },
        {
          id: '3',
          company: 'influenceTHIS Canada',
          position: 'Web Developer',
          duration: '2018-2019',
          location: 'Remote (Toronto, Canada)',
          website: 'https://influencethis.ca',
          description: 'Developed the UI and UX eco-system for a conference event platform using modular component structures with JS, SCSS, Gulp on Node.',
          technologies: ['JS', 'GULP', 'SCSS', 'Nodejs'],
          logo: 'https://images.unsplash.com/photo-1557804506-669a67965ba0?w=100&h=100&fit=crop&crop=center'
        },
        {
          id: '4',
          company: 'Upwork Inc.',
          position: 'Top Rated Web Developer',
          duration: '2017 - Present',
          location: 'Remote',
          website: 'https://upwork.com',
          description: 'Top-Rated developer on Upwork specializing in Front-end (React, WordPress) technologies with a 100% job success rate and client satisfaction rating (based on 150+ Jobs, 2500+ hours).',
          technologies: ['Javascript', 'PHP', 'HTML', 'CSS', 'Figma'],
          logo: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=100&h=100&fit=crop&crop=center'
        }
      ],

      featuredIn: [
        'TechCrunch',
        'Developer Weekly',
        'Frontend Focus',
        'JavaScript Weekly',
        'React Newsletter'
      ],

      // Form States
      isContactFormSubmitted: false,
      isReviewFormSubmitted: false,

      // Actions
      setName: (name) => set({ name }),
      setRole: (role) => set({ role }),
      setContactFormSubmitted: (isSubmitted) => set({ isContactFormSubmitted: isSubmitted }),
      setReviewFormSubmitted: (isSubmitted) => set({ isReviewFormSubmitted: isSubmitted }),
    }),
    {
      name: 'portfolio-storage',
      partialize: (state) => ({ theme: state.theme }),
    }
  )
);
