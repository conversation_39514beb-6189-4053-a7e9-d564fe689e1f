import { create } from 'zustand';

interface PortfolioState {
  name: string;
  role: string;
  isContactFormSubmitted: boolean;
  isReviewFormSubmitted: boolean;
  setName: (name: string) => void;
  setRole: (role: string) => void;
  setContactFormSubmitted: (isSubmitted: boolean) => void;
  setReviewFormSubmitted: (isSubmitted: boolean) => void;
}

export const usePortfolioStore = create<PortfolioState>((set) => ({
  name: '<PERSON>',
  role: 'A full stack developer based in Toronto.',
  isContactFormSubmitted: false,
  isReviewFormSubmitted: false,
  setName: (name) => set({ name }),
  setRole: (role) => set({ role }),
  setContactFormSubmitted: (isSubmitted) => set({ isContactFormSubmitted: isSubmitted }),
  setReviewFormSubmitted: (isSubmitted) => set({ isReviewFormSubmitted: isSubmitted }),
}));
