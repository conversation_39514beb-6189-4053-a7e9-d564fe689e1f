import { create } from 'zustand';
import { persist } from 'zustand/middleware';

type Theme = 'light' | 'dark';

interface Project {
  id: string;
  title: string;
  description: string;
  image: string;
  category: string;
  technologies: string[];
  liveUrl?: string;
  githubUrl?: string;
  featured?: boolean;
}

interface Experience {
  id: string;
  company: string;
  position: string;
  duration: string;
  location: string;
  website?: string;
  description: string;
  technologies: string[];
  logo?: string;
}

interface Expertise {
  title: string;
  description: string;
  icon: string;
}

interface PortfolioState {
  // Theme
  theme: Theme;
  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;

  // Personal Info
  name: string;
  role: string;
  title: string;
  bio: string;
  email: string;
  location: string;
  availability: string;

  // Portfolio Data
  expertise: Expertise[];
  projects: Project[];
  experience: Experience[];
  featuredIn: string[];

  // Form States
  isContactFormSubmitted: boolean;
  isReviewFormSubmitted: boolean;

  // Actions
  setName: (name: string) => void;
  setRole: (role: string) => void;
  setContactFormSubmitted: (isSubmitted: boolean) => void;
  setReviewFormSubmitted: (isSubmitted: boolean) => void;
}

export const usePortfolioStore = create<PortfolioState>()(
  persist(
    (set, get) => ({
      // Theme
      theme: 'dark',
      setTheme: (theme) => {
        set({ theme });
        if (typeof window !== 'undefined') {
          document.documentElement.setAttribute('data-theme', theme);
        }
      },
      toggleTheme: () => {
        const newTheme = get().theme === 'light' ? 'dark' : 'light';
        get().setTheme(newTheme);
      },

      // Personal Info
      name: 'Joseph Lawrence',
      role: 'Software Engineer, Frontend & App Developer',
      title: 'Full Stack Developer',
      bio: 'Passionate about creating exceptional digital experiences with modern technologies.',
      email: '<EMAIL>',
      location: 'Toronto, Canada',
      availability: 'Available for select freelance opportunities',

      // Portfolio Data
      expertise: [
        {
          title: 'Software Development',
          description: 'Experienced in both functional and OOP: JavaScript, TypeScript, Python, Java.',
          icon: 'code'
        },
        {
          title: 'Frontend Development',
          description: 'Passionate about UI/UX. Over 5 years of development experience in React, Next.js, and modern frameworks.',
          icon: 'react'
        },
        {
          title: 'Mobile Development',
          description: 'Skilled in developing cross-platform mobile applications using React Native and modern tools.',
          icon: 'mobile'
        }
      ],

      projects: [
        {
          id: '1',
          title: 'E-Commerce Platform',
          description: 'Modern e-commerce solution with advanced features and seamless user experience',
          image: '/developer-illustration.png',
          category: 'Web Development',
          technologies: ['React', 'Next.js', 'TypeScript', 'Supabase'],
          liveUrl: 'https://example.com',
          githubUrl: 'https://github.com',
          featured: true
        },
        {
          id: '2',
          title: 'Task Management App',
          description: 'Collaborative task management with real-time updates and team collaboration',
          image: '/3d-developer-character.png',
          category: 'Web Development',
          technologies: ['React', 'Node.js', 'MongoDB', 'Socket.io'],
          liveUrl: 'https://example.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '3',
          title: 'Data Visualization Dashboard',
          description: 'Interactive dashboard for complex data analysis and business insights',
          image: '/profile-placeholder.jpg',
          category: 'Data Visualization',
          technologies: ['D3.js', 'React', 'Python', 'FastAPI'],
          liveUrl: 'https://example.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '4',
          title: 'Mobile Banking App',
          description: 'Secure mobile banking application with modern UI/UX design',
          image: '/developer-illustration.png',
          category: 'Mobile Development',
          technologies: ['React Native', 'TypeScript', 'Firebase', 'Redux'],
          liveUrl: 'https://example.com',
          githubUrl: 'https://github.com'
        },
        {
          id: '5',
          title: 'Portfolio Website',
          description: 'Personal portfolio website with interactive 3D elements and animations',
          image: '/3d-developer-character.png',
          category: 'Web Development',
          technologies: ['Next.js', 'Three.js', 'Framer Motion', 'Tailwind CSS'],
          liveUrl: 'https://example.com',
          githubUrl: 'https://github.com'
        }
      ],

      experience: [
        {
          id: '1',
          company: 'Tech Solutions Inc.',
          position: 'Senior Frontend Developer',
          duration: '2022 - Present',
          location: 'Toronto, Canada',
          website: 'https://techsolutions.com',
          description: 'Led frontend development team to design and develop robust web applications using React/Next.js, focusing on responsive design, scalability, and enhanced user experience.',
          technologies: ['React', 'Next.js', 'TypeScript', 'Tailwind CSS'],
          logo: '/profile-placeholder.jpg'
        },
        {
          id: '2',
          company: 'Digital Agency',
          position: 'Full Stack Developer',
          duration: '2020 - 2022',
          location: 'Remote',
          website: 'https://digitalagency.com',
          description: 'Developed end-to-end web solutions for various clients, utilizing modern technologies and best practices.',
          technologies: ['JavaScript', 'Node.js', 'React', 'MongoDB'],
          logo: '/profile-placeholder.jpg'
        },
        {
          id: '3',
          company: 'Freelance Developer',
          position: 'Top Rated Web Developer',
          duration: '2018 - Present',
          location: 'Remote',
          website: 'https://upwork.com',
          description: 'Top-Rated developer specializing in Frontend technologies with a 100% job success rate and client satisfaction rating (based on 50+ Jobs, 1000+ hours).',
          technologies: ['JavaScript', 'React', 'WordPress', 'Figma'],
          logo: '/profile-placeholder.jpg'
        }
      ],

      featuredIn: [
        'TechCrunch',
        'Developer Weekly',
        'Frontend Focus',
        'JavaScript Weekly',
        'React Newsletter'
      ],

      // Form States
      isContactFormSubmitted: false,
      isReviewFormSubmitted: false,

      // Actions
      setName: (name) => set({ name }),
      setRole: (role) => set({ role }),
      setContactFormSubmitted: (isSubmitted) => set({ isContactFormSubmitted: isSubmitted }),
      setReviewFormSubmitted: (isSubmitted) => set({ isReviewFormSubmitted: isSubmitted }),
    }),
    {
      name: 'portfolio-storage',
      partialize: (state) => ({ theme: state.theme }),
    }
  )
);
