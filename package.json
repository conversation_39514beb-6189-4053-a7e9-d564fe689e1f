{"name": "folio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.0.8", "@react-three/fiber": "^9.1.2", "@supabase/supabase-js": "^2.49.10", "framer-motion": "^12.12.1", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "three": "^0.176.0", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.5.3", "tailwindcss": "^4.1.7", "typescript": "^5"}}