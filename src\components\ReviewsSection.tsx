'use client';

import { useState, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { FaStar, FaComments, FaPen } from 'react-icons/fa';
import ReviewSubmissionForm from './ReviewSubmissionForm';
import ReviewsDisplay from './ReviewsDisplay';

const ReviewsSection = () => {
  const [activeTab, setActiveTab] = useState<'view' | 'submit'>('view');
  const [refreshKey, setRefreshKey] = useState(0);
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: false, margin: '-100px' });

  const handleSubmitSuccess = () => {
    // Refresh the reviews display and switch to view tab
    setRefreshKey(prev => prev + 1);
    setActiveTab('view');
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.15,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0, scale: 0.9 },
    visible: {
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 12
      }
    }
  };

  const tabVariants = {
    inactive: { scale: 1, opacity: 0.7 },
    active: {
      scale: 1.05,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 20
      }
    }
  };

  return (
    <section 
      id="reviews" 
      ref={sectionRef}
      className="py-24 relative overflow-hidden"
      style={{ background: 'var(--card-bg)' }}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 opacity-30">
        <div className="absolute top-0 left-0 w-full h-full bg-[var(--background)] opacity-90"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 rounded-full bg-[var(--accent-secondary)] filter blur-[150px] opacity-10 animate-pulse"></div>
        <div className="absolute top-0 right-0 w-80 h-80 rounded-full bg-[var(--accent-primary)] filter blur-[120px] opacity-10 animate-pulse delay-1000"></div>
      </div>

      <div className="container mx-auto px-4 md:px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-4 inline-block relative">
              <span className="text-foreground relative z-10">Client Reviews</span>
              <span className="absolute bottom-0 left-0 w-full h-3 bg-[var(--accent-tertiary)] opacity-20 rounded"></span>
            </h2>
          </motion.div>
          <motion.p 
            className="text-foreground/70 max-w-2xl mx-auto text-lg"
            initial={{ opacity: 0 }}
            animate={isInView ? { opacity: 1 } : { opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            See what clients say about working with me, or share your own experience
          </motion.p>
        </div>

        {/* Enhanced Tab Navigation */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="flex justify-center mb-12"
        >
          <div className="glass rounded-full p-1 flex border border-[rgba(255,255,255,0.1)]">
            <motion.button
              onClick={() => setActiveTab('view')}
              className={`
                flex items-center px-6 py-3 rounded-full transition-all duration-300 relative
                ${activeTab === 'view'
                  ? 'bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white shadow-lg'
                  : 'text-foreground/70 hover:text-foreground'
                }
              `}
              variants={tabVariants}
              animate={activeTab === 'view' ? 'active' : 'inactive'}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                animate={activeTab === 'view' ? { rotate: 360 } : { rotate: 0 }}
                transition={{ duration: 0.5 }}
              >
                <FaComments className="mr-2" />
              </motion.div>
              View Reviews
            </motion.button>
            <motion.button
              onClick={() => setActiveTab('submit')}
              className={`
                flex items-center px-6 py-3 rounded-full transition-all duration-300 relative
                ${activeTab === 'submit'
                  ? 'bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] text-white shadow-lg'
                  : 'text-foreground/70 hover:text-foreground'
                }
              `}
              variants={tabVariants}
              animate={activeTab === 'submit' ? 'active' : 'inactive'}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <motion.div
                animate={activeTab === 'submit' ? { rotate: 360 } : { rotate: 0 }}
                transition={{ duration: 0.5 }}
              >
                <FaPen className="mr-2" />
              </motion.div>
              Write Review
            </motion.button>
          </div>
        </motion.div>

        {/* Tab Content */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          {activeTab === 'view' ? (
            <motion.div
              key={`reviews-${refreshKey}`}
              variants={itemVariants}
              initial="hidden"
              animate="visible"
            >
              <ReviewsDisplay />
            </motion.div>
          ) : (
            <motion.div
              variants={itemVariants}
              initial="hidden"
              animate="visible"
              className="max-w-2xl mx-auto"
            >
              <div className="mb-8 text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-gradient-to-r from-[var(--accent-primary)] to-[var(--accent-secondary)] mb-4">
                  <FaStar className="text-white text-2xl" />
                </div>
                <h3 className="text-2xl font-bold mb-2 text-foreground">Share Your Experience</h3>
                <p className="text-foreground/70">
                  Your feedback helps me improve and helps others understand what it's like to work with me.
                </p>
              </div>
              <ReviewSubmissionForm onSubmitSuccess={handleSubmitSuccess} />
            </motion.div>
          )}
        </motion.div>

        {/* Stats Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto"
        >
          {[
            { icon: FaStar, label: 'Quality Focus', value: '5-Star', color: 'var(--accent-primary)' },
            { icon: FaComments, label: 'Client Feedback', value: 'Valued', color: 'var(--accent-secondary)' },
            { icon: FaPen, label: 'Your Voice', value: 'Matters', color: 'var(--accent-tertiary)' }
          ].map((stat, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              className="text-center p-6 bg-gray-800 rounded-lg hover:bg-gray-750 transition-all duration-300 group"
            >
              <div
                className="w-12 h-12 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform"
                style={{ backgroundColor: `${stat.color}20`, color: stat.color }}
              >
                <stat.icon className="text-xl" />
              </div>
              <h4 className="text-2xl font-bold text-foreground mb-1">{stat.value}</h4>
              <p className="text-foreground/70 text-sm">{stat.label}</p>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </section>
  );
};

export default ReviewsSection;
